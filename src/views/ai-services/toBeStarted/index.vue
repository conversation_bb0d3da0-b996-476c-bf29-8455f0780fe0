<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="方案号">
                <el-input
                  v-model="queryParams.PrescriptionId"
                  type="link"
                  placeholder="方案号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="手机号">
                <el-input
                  v-model="queryParams.PhoneNumber"
                  type="link"
                  placeholder="手机号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column label="方案号" prop="PrescriptionIdStr" align="center" width="150" />
          <el-table-column label="类型" align="center" width="100">
            <template #default="scope">
              {{ flowStateEnum[scope.row.Type] }}
            </template>
          </el-table-column>
          <el-table-column label="患者名称" prop="PatientName" width="120" align="center" />
          <el-table-column
            label="患者手机号"
            prop="PatientPhoneNumber"
            width="120"
            align="center"
          />
          <el-table-column
            label="创建时间"
            prop="CreatedTime"
            align="center"
            width="180"
            :formatter="tableDateFormat"
          />
          <el-table-column label="是否测试数据" prop="IsTest" align="center" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.IsTest" type="success">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="症状" align="center">
            <template #default="scope">
              <div>{{ handleGetPatientSymptoms(scope.row, "ChiefComplaint") }}</div>
              <div>{{ handleGetPatientSymptoms(scope.row, "AssistantDiagnosis") }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="140">
            <template #default="scope">
              <el-button type="primary" link @click="handleAddDiagnosis(scope.row)">
                添加诊断
              </el-button>
              <el-button type="primary" link @click="handleStartFlow(scope.row)">开启</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageInput.PageIndex"
          v-model:limit="queryParams.PageInput.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
const flowStateEnum = ["磁疗", "脊柱", "足底"];
import { useTableConfig } from "@/hooks/useTableConfig";
import { FlowItem, FlowListInputDTO } from "@/api/aipowered/types";
import AIPowered_Api from "@/api/aipowered";

defineOptions({
  name: "AiServicesToBeStarted",
});

const queryParams = ref<FlowListInputDTO>({
  PrescriptionId: null,
  PhoneNumber: null,
  Status: null,
  Type: null,
  PageInput: {
    PageIndex: 1,
    PageSize: 20,
  },
});
// const timeRange = ref<[string, string]>([
//   dayjs().format("YYYY-MM-01"),
//   dayjs().format("YYYY-MM-DD"),
// ]);

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const { tableLoading, pageData, total, tableRef, tableDateFormat, tableFluidHeight, tableResize } =
  useTableConfig<FlowItem>();

const handleQuery = () => {
  queryParams.value.PageInput.PageIndex = 1;
  handleGetTableList();
};

const handleGetPatientSymptoms = (
  row: FlowItem,
  type: "ChiefComplaint" | "AssistantDiagnosis"
): string => {
  let symptoms = "";
  let typeHeader = type === "ChiefComplaint" ? "患者主诉：" : "医助辅助：";
  if (row[type]) {
    symptoms += typeHeader + row[type];
  }
  return symptoms;
};

const handleAddDiagnosis = (row: FlowItem) => {};

const handleGetTableList = async () => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  if (!copyData.PrescriptionId) {
    delete copyData.PrescriptionId;
  }
  if (!copyData.PhoneNumber) {
    delete copyData.PhoneNumber;
  }
  tableLoading.value = true;
  const res = await AIPowered_Api.getPendingFlowList(copyData);
  if (res.Type === 200) {
    pageData.value = res.Data.Rows;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

const handleStartFlow = (row: FlowItem) => {
  ElMessageBox.confirm("确定启动该流程吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await AIPowered_Api.launchFlow({ PrescriptionId: row.PrescriptionIdStr! });
      if (res.Type === 200) {
        ElMessage.success("启动成功");
        handleGetTableList();
      }
    })
    .catch((err) => {
      ElMessage.error(err.Message);
    });
};

// watch(timeRange, (newVal) => {
//   queryParams.value.QueryStartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
//   queryParams.value.QueryEndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
// });

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
